/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #007AFF;
    --primary-hover: #0056CC;
    --secondary-color: #5856D6;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --error-color: #FF3B30;
    --background: #F2F2F7;
    --surface: #FFFFFF;
    --surface-secondary: #F9F9F9;
    --text-primary: #1C1C1E;
    --text-secondary: #8E8E93;
    --border: #E5E5EA;
    --border-focus: #007AFF;
    --shadow: rgba(0, 0, 0, 0.1);
    --shadow-hover: rgba(0, 0, 0, 0.15);
    --border-radius: 12px;
    --border-radius-small: 8px;
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, var(--background) 0%, #E8E8ED 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 480px;
    margin: 0 auto;
    background: var(--surface);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 40px var(--shadow);
    overflow: hidden;
    animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 32px 24px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translate(-50%, -50%) rotate(0deg); }
    50% { transform: translate(-50%, -50%) rotate(180deg); }
}

.apple-logo {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    margin-bottom: 16px;
    backdrop-filter: blur(10px);
}

.header h1 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
}

.subtitle {
    font-size: 16px;
    opacity: 0.9;
    font-weight: 400;
}

.main-content {
    padding: 32px 24px;
}

.credentials-section {
    margin-bottom: 32px;
}

.input-group {
    margin-bottom: 24px;
}

.input-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper input {
    width: 100%;
    padding: 16px 50px 16px 16px;
    border: 2px solid var(--border);
    border-radius: var(--border-radius-small);
    font-size: 16px;
    font-family: inherit;
    background: var(--surface-secondary);
    transition: var(--transition);
    outline: none;
}

.input-wrapper input:focus {
    border-color: var(--border-focus);
    background: var(--surface);
    box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
}

.input-wrapper input::placeholder {
    color: var(--text-secondary);
}

.copy-btn, .toggle-password {
    position: absolute;
    right: 8px;
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: var(--text-secondary);
    border-radius: var(--border-radius-small);
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.toggle-password {
    right: 40px;
}

.copy-btn:hover, .toggle-password:hover {
    background: var(--surface-secondary);
    color: var(--primary-color);
    transform: scale(1.05);
}

.actions {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
}

.primary-btn, .secondary-btn {
    flex: 1;
    padding: 16px 24px;
    border: none;
    border-radius: var(--border-radius-small);
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-family: inherit;
}

.primary-btn {
    background: var(--primary-color);
    color: white;
}

.primary-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3);
}

.secondary-btn {
    background: var(--surface-secondary);
    color: var(--text-primary);
    border: 2px solid var(--border);
}

.secondary-btn:hover {
    background: var(--surface);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.status-message {
    padding: 12px 16px;
    border-radius: var(--border-radius-small);
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    opacity: 0;
    transform: translateY(10px);
    transition: var(--transition);
}

.status-message.show {
    opacity: 1;
    transform: translateY(0);
}

.status-message.success {
    background: rgba(52, 199, 89, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(52, 199, 89, 0.2);
}

.status-message.error {
    background: rgba(255, 59, 48, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(255, 59, 48, 0.2);
}

.footer {
    background: var(--surface-secondary);
    padding: 24px;
    text-align: center;
    border-top: 1px solid var(--border);
}

.footer p {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.footer p:last-child {
    margin-bottom: 0;
}

.qr-hint {
    font-weight: 500;
    color: var(--primary-color);
}

/* Mobile optimizations */
@media (max-width: 480px) {
    body {
        padding: 10px;
    }
    
    .container {
        margin: 0;
        border-radius: 0;
        min-height: 100vh;
    }
    
    .header {
        padding: 24px 20px;
    }
    
    .main-content {
        padding: 24px 20px;
    }
    
    .actions {
        flex-direction: column;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --background: #000000;
        --surface: #1C1C1E;
        --surface-secondary: #2C2C2E;
        --text-primary: #FFFFFF;
        --text-secondary: #8E8E93;
        --border: #38383A;
    }
}
