// Apple ID Manager - Client-side credential management
class AppleIDManager {
    constructor() {
        this.init();
    }

    init() {
        this.loadCredentials();
        this.setupEventListeners();
        this.setupAutoSave();
    }

    setupEventListeners() {
        // Auto-save on input change
        const inputs = document.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this.debounce(this.autoSave.bind(this), 1000)();
            });
        });

        // Enter key handling
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                this.copyBoth();
            }
        });
    }

    setupAutoSave() {
        // Save credentials every 30 seconds if there are changes
        setInterval(() => {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (email || password) {
                this.saveCredentials(false); // Silent save
            }
        }, 30000);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    autoSave() {
        this.saveCredentials(false); // Silent auto-save
    }

    saveCredentials(showMessage = true) {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        if (!email && !password) {
            if (showMessage) {
                this.showMessage('Please enter your credentials first', 'error');
            }
            return;
        }

        try {
            const credentials = {
                email: email,
                password: password,
                lastUpdated: new Date().toISOString(),
                version: '1.0'
            };

            localStorage.setItem('appleIDCredentials', JSON.stringify(credentials));
            
            if (showMessage) {
                this.showMessage('✅ Credentials saved successfully!', 'success');
            }
        } catch (error) {
            console.error('Error saving credentials:', error);
            if (showMessage) {
                this.showMessage('❌ Error saving credentials', 'error');
            }
        }
    }

    loadCredentials() {
        try {
            const saved = localStorage.getItem('appleIDCredentials');
            if (saved) {
                const credentials = JSON.parse(saved);
                document.getElementById('email').value = credentials.email || '';
                document.getElementById('password').value = credentials.password || '';
            }
        } catch (error) {
            console.error('Error loading credentials:', error);
        }
    }

    async copyToClipboard(fieldId) {
        const field = document.getElementById(fieldId);
        const value = field.value;

        if (!value) {
            this.showMessage(`❌ ${fieldId === 'email' ? 'Email' : 'Password'} is empty`, 'error');
            return;
        }

        try {
            await navigator.clipboard.writeText(value);
            this.showMessage(`✅ ${fieldId === 'email' ? 'Email' : 'Password'} copied to clipboard!`, 'success');
            
            // Visual feedback
            const button = field.parentElement.querySelector('.copy-btn');
            button.style.transform = 'scale(1.2)';
            button.style.color = 'var(--success-color)';
            
            setTimeout(() => {
                button.style.transform = '';
                button.style.color = '';
            }, 200);
            
        } catch (error) {
            console.error('Copy failed:', error);
            // Fallback for older browsers
            this.fallbackCopy(value, fieldId);
        }
    }

    fallbackCopy(text, fieldId) {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        try {
            document.execCommand('copy');
            this.showMessage(`✅ ${fieldId === 'email' ? 'Email' : 'Password'} copied to clipboard!`, 'success');
        } catch (error) {
            this.showMessage('❌ Copy failed. Please copy manually.', 'error');
        }
        
        document.body.removeChild(textArea);
    }

    async copyBoth() {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        if (!email && !password) {
            this.showMessage('❌ No credentials to copy', 'error');
            return;
        }

        const credentials = [];
        if (email) credentials.push(`Email: ${email}`);
        if (password) credentials.push(`Password: ${password}`);
        
        const text = credentials.join('\n');

        try {
            await navigator.clipboard.writeText(text);
            this.showMessage('✅ Both credentials copied to clipboard!', 'success');
        } catch (error) {
            this.fallbackCopy(text, 'both');
        }
    }

    togglePassword() {
        const passwordField = document.getElementById('password');
        const eyeIcon = document.getElementById('eye-icon');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            eyeIcon.innerHTML = `
                <path d="M17.94 17.94A10.07 10.07 0 0112 20c-7 0-11-8-11-8a18.45 18.45 0 015.06-5.94M9.9 4.24A9.12 9.12 0 0112 4c7 0 11 8 11 8a18.5 18.5 0 01-2.16 3.19m-6.72-1.07a3 3 0 11-4.24-4.24" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="1" y1="1" x2="23" y2="23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            `;
        } else {
            passwordField.type = 'password';
            eyeIcon.innerHTML = `
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            `;
        }
    }

    showMessage(message, type) {
        const statusElement = document.getElementById('statusMessage');
        statusElement.textContent = message;
        statusElement.className = `status-message ${type} show`;
        
        setTimeout(() => {
            statusElement.classList.remove('show');
        }, 3000);
    }

    clearCredentials() {
        if (confirm('Are you sure you want to clear all saved credentials?')) {
            localStorage.removeItem('appleIDCredentials');
            document.getElementById('email').value = '';
            document.getElementById('password').value = '';
            this.showMessage('✅ Credentials cleared', 'success');
        }
    }
}

// Global functions for HTML onclick handlers
function copyToClipboard(fieldId) {
    window.appleIDManager.copyToClipboard(fieldId);
}

function copyBoth() {
    window.appleIDManager.copyBoth();
}

function togglePassword() {
    window.appleIDManager.togglePassword();
}

function saveCredentials() {
    window.appleIDManager.saveCredentials();
}

function clearCredentials() {
    window.appleIDManager.clearCredentials();
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    window.appleIDManager = new AppleIDManager();
});

// Service Worker registration for offline functionality
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
