# Apple ID Manager

A modern, secure, and elegant web application for managing your Apple ID credentials with QR code access.

## Features

- 🔒 **Secure Local Storage**: Credentials are stored only in your browser's local storage
- 📱 **Mobile Optimized**: Perfect responsive design for mobile devices
- 📋 **One-Click Copy**: Copy email, password, or both with a single click
- 🎨 **Modern Design**: Clean, Apple-inspired interface with smooth animations
- 🌙 **Dark Mode**: Automatic dark mode support based on system preferences
- 📶 **Offline Ready**: Works offline with service worker caching
- ⚡ **Auto-Save**: Automatically saves your credentials as you type

## How to Use

1. **Enter Your Credentials**: Input your Apple ID email and password
2. **Save**: Click "Save Credentials" or they'll auto-save as you type
3. **Copy**: Use the copy buttons to quickly copy credentials to clipboard
4. **QR Code Access**: Generate a QR code pointing to your hosted website

## QR Code Generation

To create a QR code for easy mobile access:

### Option 1: Online QR Code Generators
1. Host this website (see deployment options below)
2. Copy your website URL
3. Use any QR code generator like:
   - [QR Code Generator](https://www.qr-code-generator.com/)
   - [QRCode Monkey](https://www.qrcode-monkey.com/)
   - [Google Charts QR API](https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=YOUR_URL_HERE)

### Option 2: Command Line (if you have Node.js)
```bash
npm install -g qrcode-generator-cli
qrcode "https://your-website-url.com"
```

## Deployment Options

### Option 1: GitHub Pages (Free)
1. Create a new GitHub repository
2. Upload these files to the repository
3. Go to Settings > Pages
4. Select "Deploy from a branch" and choose "main"
5. Your site will be available at `https://yourusername.github.io/repository-name`

### Option 2: Netlify (Free)
1. Go to [Netlify](https://netlify.com)
2. Drag and drop your project folder
3. Get instant deployment with HTTPS

### Option 3: Vercel (Free)
1. Go to [Vercel](https://vercel.com)
2. Import your project from GitHub or upload directly
3. Get instant deployment with HTTPS

### Option 4: Local Network Access
1. Start a simple HTTP server:
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   
   # Node.js (if you have http-server installed)
   npx http-server
   ```
2. Access via `http://your-local-ip:8000`
3. Generate QR code with this local URL

## Security Notes

- ✅ **Client-Side Only**: No server involved, credentials never leave your device
- ✅ **Local Storage**: Data stored in browser's secure local storage
- ✅ **HTTPS Ready**: Works with HTTPS for secure transmission
- ⚠️ **Browser Dependent**: Clearing browser data will remove saved credentials
- ⚠️ **Device Specific**: Credentials are tied to the specific browser/device

## Browser Compatibility

- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Keyboard Shortcuts

- `Ctrl/Cmd + Enter`: Copy both email and password
- `Tab`: Navigate between fields
- `Enter`: In password field, copies both credentials

## Customization

You can easily customize the appearance by modifying the CSS variables in `styles.css`:

```css
:root {
    --primary-color: #007AFF;    /* Change primary color */
    --border-radius: 12px;       /* Adjust border radius */
    /* ... other variables */
}
```

## File Structure

```
apple-id-manager/
├── index.html          # Main HTML file
├── styles.css          # CSS styling
├── script.js           # JavaScript functionality
├── sw.js              # Service Worker for offline support
└── README.md          # This file
```

## License

This project is open source and available under the MIT License.
